package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/spf13/cast"

	v1 "gitlab.zalopay.vn/fin/installment/installment-service/api/management_service/installment/v1"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/model"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/errorkit"
	"gitlab.zalopay.vn/fin/installment/installment-service/pkg/middleware/auth"
)

func (s *InstallmentService) GetClientInstallment(ctx context.Context,
	req *v1.GetClientInstallmentRequest) (*v1.GetClientInstallmentResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	resp, err := s.instUc.GetFullInstallmentByZalopayAndZPTransID(ctx, zalopayID, req.ZpTransId)
	if err != nil {
		return nil, convertToTransportError(err)
	}

	installment, err := convertToInstallmentData(resp)
	if err != nil {
		return nil, convertToTransportError(err)
	}

	// In scope get installment info, we don't need to return the full early discharge info
	repaySchedules := convertToInstRepaySchedules(resp.Repays)
	normalizeData := s.instUc.BuildNormalizeEarlyDischarge(resp.Info)
	earlyDischarge := convertToEarlyDischarge(normalizeData)
	dischargeResult := &v1.EarlyDischarge{Info: earlyDischarge.Info}

	result := &v1.GetClientInstallmentResponse{
		Installment:        installment,
		EarlyDischarge:     dischargeResult,
		RepaymentSchedules: repaySchedules,
	}
	return result, nil
}

func (s *InstallmentService) GetInstallmentStatus(ctx context.Context,
	req *v1.GetInstallmentStatusRequest) (*v1.GetInstallmentStatusResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	resp, err := s.instUc.GetFullInstallmentByZPTransID(ctx, req.GetZpTransId())
	if err != nil {
		s.logger.WithContext(ctx).Errorf("GetInstallmentStatus has error: %v", err)
		return nil, convertToTransportError(err)
	}
	if !req.GetForceLatest() {
		return &v1.GetInstallmentStatusResponse{
			Status: convertToInstallmentStatus(resp.Info.Status),
		}, nil
	}

	inst, err := s.instUc.SyncInstallmentData(ctx, resp.Info)
	if err != nil {
		s.logger.WithContext(ctx).Errorf("SyncInstallmentData has error: %v", err)
		return nil, convertToTransportError(err)
	}

	return &v1.GetInstallmentStatusResponse{
		Info:   convertToInstallmentBase(inst),
		Status: convertToInstallmentStatus(inst.Status),
	}, nil
}

func (s *InstallmentService) GetClientEarlyDischarge(ctx context.Context, req *v1.GetClientEarlyDischargeRequest) (*v1.GetClientEarlyDischargeResponse, error) {
	session, _ := auth.FromContext(ctx)
	zalopayID := cast.ToInt64(session.GetZalopayId())

	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	resp, err := s.instUc.GetLatestEarlyDischarge(ctx, &dto.EarlyDischargeQueryParams{
		ZPTransID: req.GetZpTransId(),
	})
	if err != nil {
		s.logger.WithContext(ctx).Errorf("GetClientEarlyDischarge has error: %v", err)
		return nil, convertToTransportError(err)
	}
	if resp == nil || resp.GetZalopayID() != zalopayID {
		return nil, errors.NotFound(errorkit.CodeDataNotFound.String(), "early discharge not found")
	}

	return &v1.GetClientEarlyDischargeResponse{
		EarlyDischarge: convertToEarlyDischarge(resp),
	}, nil
}

func (s *InstallmentService) GetEarlyDischargeRefund(ctx context.Context, req *v1.GetEarlyDischargeRefundRequest) (*v1.GetEarlyDischargeRefundResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}

	params := &dto.EarlyDischargeQueryParams{
		ZPTransID: req.GetZpTransId(),
		ForceSync: req.GetForceLatest(),
	}
	resp, err := s.instUc.GetLatestEarlyDischarge(ctx, params)
	if err != nil {
		s.logger.WithContext(ctx).Errorf("GetEarlyDischargeRefund has error: %v", err)
		return nil, convertToTransportError(err)
	}

	return &v1.GetEarlyDischargeRefundResponse{
		DischargeInfo: convertToEarlyDischargeForRefund(resp),
	}, nil
}

func (s *InstallmentService) NotifyInstallmentRefund(ctx context.Context, req *v1.InstallmentRefund) (*v1.NotifyInstallmentRefundResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), err.Error())
	}
	
	// Additional validation for ZpTransId
	if req.GetZpTransId() <= 0 {
		return nil, errors.BadRequest(errorkit.CodeBadRequest.String(), "zpTransId must be greater than 0")
	}

	zpTransID := req.GetZpTransId()
	refundInfo := &model.InstallmentRefund{
		UpdateVersion:     int(req.GetVersion()),
		NetRefundAmount:   req.GetNetRefundAmount(),
		TotalRefundAmount: req.GetTotalRefundAmount(),
		UserTopupAmount:   req.GetUserTopupAmount(),
		UserTopupRequired: req.GetUserTopupRequired(),
	}
	err := s.instUc.HandleRefundEventNotified(ctx, zpTransID, refundInfo)
	if err != nil {
		s.logger.WithContext(ctx).Errorf("NotifyInstallmentRefund has error: %v", err)
		return nil, convertToTransportError(err)
	}

	return &v1.NotifyInstallmentRefundResponse{}, nil
}
