package job_task

import (
	"context"
	"fmt"
	"slices"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"go.temporal.io/api/enums/v1"
	"go.temporal.io/api/filter/v1"
	"go.temporal.io/api/serviceerror"
	"go.temporal.io/api/workflowservice/v1"
	"go.temporal.io/sdk/client"

	"gitlab.zalopay.vn/fin/installment/installment-service/config"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/dto"
	_interface "gitlab.zalopay.vn/fin/installment/installment-service/internal/management/usecase/interface"
	"gitlab.zalopay.vn/fin/installment/installment-service/internal/management/utils"
)

type jobTask struct {
	logger  *log.Helper
	tempCli client.Client
	config  *config.Management_Schedulers
}

func NewJobTaskMgmt(conf *config.Management, tempCli client.Client, kLogger log.Logger) _interface.JobTaskMgmt {
	logger := log.NewHelper(log.With(kLogger, "adapters", "job-task"))
	return &jobTask{
		logger:  logger,
		tempCli: tempCli,
		config:  conf.GetSchedulers(),
	}
}

func (s jobTask) ExecuteSyncInstallmentDailyTask(ctx context.Context) error {
	logger := s.logger.WithContext(ctx)
	timeNow := time.Now()
	wfConfig := s.config.GetSyncInstallmentsDaily()
	wfIdentifier := fmt.Sprintf(utils.WfIDSyncInstallmentsDaily.String(), timeNow.Format(time.DateOnly))
	wfReusePolicy := enums.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE

	s.logger.WithContext(ctx).Info("Start to execute sync installment period task")

	wfOption := client.StartWorkflowOptions{
		ID:                    wfIdentifier,
		TaskQueue:             wfConfig.GetQueueName(),
		WorkflowIDReusePolicy: wfReusePolicy,
	}

	wfRun, err := s.tempCli.ExecuteWorkflow(ctx, wfOption, wfConfig.GetWorkflowType())
	if err != nil {
		logger.Errorf("Fail to register sync installment daily task, error=%v", err)
		return errors.Wrap(err, "register sync installment daily task failed")
	}

	logger.Infof("Register sync installment daily task success, runID=%s", wfRun.GetRunID())
	return nil
}

func (s jobTask) CanExecuteSyncInstallmentsDailyTask(ctx context.Context) (bool, error) {
	wfType := s.config.GetSyncInstallmentsDaily().GetWorkflowType()
	wfList, err := s.tempCli.ListOpenWorkflow(ctx, &workflowservice.ListOpenWorkflowExecutionsRequest{
		MaximumPageSize: 1,
		Filters: &workflowservice.ListOpenWorkflowExecutionsRequest_TypeFilter{
			TypeFilter: &filter.WorkflowTypeFilter{Name: wfType},
		},
	})
	if err != nil {
		if notFoundErr := err.(*serviceerror.NotFound); notFoundErr != nil {
			return true, nil
		}
		return false, errors.Wrap(err, "cannot query information of installment daily sync task")
	}
	if len(wfList.Executions) == 0 {
		return true, nil
	}

	wfInfo := wfList.Executions[0]
	wfStatus := wfInfo.GetStatus()
	unAllowedStatuses := []enums.WorkflowExecutionStatus{
		enums.WORKFLOW_EXECUTION_STATUS_RUNNING,
	}

	return !slices.Contains(unAllowedStatuses, wfStatus), nil
}

func (s jobTask) ExecuteSyncAccountBalanceAfterStatementTask(
	ctx context.Context, params *dto.SyncAccountBalanceAfterStatementParams) error {
	logger := s.logger.WithContext(ctx)
	wfConfig := s.config.GetSyncAccountBalance()
	stmtDateStr := params.StatementDate.Format("********")
	wfIdPattern := utils.WfIDSyncAccountBalanceAfterStatement.String()
	wfIdentifier := fmt.Sprintf(wfIdPattern, stmtDateStr, params.ZalopayID, params.AccountID)
	wfReusePolicy := enums.WORKFLOW_ID_REUSE_POLICY_TERMINATE_IF_RUNNING

	s.logger.WithContext(ctx).Info("Start to execute sync account balance task")

	wfParams := params
	wfOption := client.StartWorkflowOptions{
		ID:                    wfIdentifier,
		TaskQueue:             wfConfig.GetQueueName(),
		WorkflowIDReusePolicy: wfReusePolicy,
	}

	wfRun, err := s.tempCli.ExecuteWorkflow(ctx, wfOption, wfConfig.GetWorkflowType(), wfParams)
	if err != nil {
		logger.Errorf("Fail to register sync account balance task, error=%v", err)
		return errors.Wrap(err, "register sync account balance task failed")
	}

	logger.Infof("Register sync account balance task success, runID=%s", wfRun.GetRunID())
	return nil
}

func (s *jobTask) ExecuteSyncLatestStatementTask(ctx context.Context, params *dto.SyncLatestStatementParams) error {
	logger := s.logger.WithContext(ctx)
	wfConfig := s.config.GetSyncLatestStatement()
	wfIdPattern := utils.WfIDSyncStatementsLatest.String()
	wfIdentifier := fmt.Sprintf(wfIdPattern, params.ZalopayID, params.AccountID)
	wfReusePolicy := enums.WORKFLOW_ID_REUSE_POLICY_TERMINATE_IF_RUNNING

	s.logger.WithContext(ctx).Info("Start to execute sync latest statement task")

	wfParams := params
	wfOption := client.StartWorkflowOptions{
		ID:                    wfIdentifier,
		TaskQueue:             wfConfig.GetQueueName(),
		WorkflowIDReusePolicy: wfReusePolicy,
	}

	wfRun, err := s.tempCli.ExecuteWorkflow(ctx, wfOption, wfConfig.GetWorkflowType(), wfParams)
	if err != nil {
		logger.Errorf("Fail to register sync latest statement task, error=%v", err)
		return errors.Wrap(err, "register sync latest statement task failed")
	}

	logger.Infof("Register sync latest statement task success, runID=%s", wfRun.GetRunID())
	return nil
}

func (s *jobTask) ExecuteSyncInstallmentTask(ctx context.Context, params *dto.SyncInstallmentParams) error {
	logger := s.logger.WithContext(ctx)
	wfConfig := s.config.GetSyncInstallmentInfo()
	wfIdPattern := utils.WfIDSyncInstallment.String()
	wfIdentifier := fmt.Sprintf(wfIdPattern, params.InstID, params.ZPTransID)
	wfReusePolicy := enums.WORKFLOW_ID_REUSE_POLICY_TERMINATE_IF_RUNNING

	s.logger.WithContext(ctx).Info("Start to execute sync installment task")

	wfParams := params
	wfOption := client.StartWorkflowOptions{
		ID:                    wfIdentifier,
		TaskQueue:             wfConfig.GetQueueName(),
		WorkflowIDReusePolicy: wfReusePolicy,
	}

	wfRun, err := s.tempCli.ExecuteWorkflow(ctx, wfOption, wfConfig.GetWorkflowType(), wfParams)
	if err != nil {
		logger.Errorf("Fail to register sync installment task, error=%v", err)
		return errors.Wrap(err, "register sync installment task failed")
	}

	logger.Infof("Register sync installment task success, runID=%s", wfRun.GetRunID())
	return nil
}

func (s *jobTask) ExecuteSyncAccountBalanceAfterDischargeTask(ctx context.Context, params *dto.SyncAccountBalanceAfterDischargeParams) error {
	logger := s.logger.WithContext(ctx)
	wfConfig := s.config.GetSyncAccountBalance()
	wfIdPattern := utils.WfIDSyncAccountBalanceAfterDischarge.String()
	wfIdentifier := fmt.Sprintf(wfIdPattern, params.ZalopayID, params.AccountID)
	wfReusePolicy := enums.WORKFLOW_ID_REUSE_POLICY_TERMINATE_IF_RUNNING

	s.logger.WithContext(ctx).Info("Start to execute sync account balance after discharge task")

	wfParams := params
	wfOption := client.StartWorkflowOptions{
		ID:                    wfIdentifier,
		TaskQueue:             wfConfig.GetQueueName(),
		WorkflowIDReusePolicy: wfReusePolicy,
	}

	wfRun, err := s.tempCli.ExecuteWorkflow(ctx, wfOption, wfConfig.GetWorkflowType(), wfParams)
	if err != nil {
		logger.Errorf("Fail to register sync account balance task, error=%v", err)
		return errors.Wrap(err, "register sync account balance task failed")
	}

	logger.Infof("Register sync account balance task success, runID=%s", wfRun.GetRunID())
	return nil
}
